

// src/app/app/worksheet/page.tsx
'use client';

import { useSession } from 'next-auth/react';
import { useEffect, useState } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { motion } from 'framer-motion';
import Navbar1 from '../../../../components/navbar1';
import FreelancerTopNavbar from '../../../../components/freelancer-dashboard/top-navbar';
import CommissionerTopNavbar from '../../../../components/commissioner-dashboard/top-navbar';
import AIChatThread from '../../../../components/new-landing/worksheet/ai-chat-thread';
import PromptToGigChat from '../../../../components/new-landing/worksheet/prompt-to-gig-chat';
import PromptToProjectChat from '../../../../components/new-landing/worksheet/prompt-to-project-chat';

export default function WorksheetPage() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const searchParams = useSearchParams();
  const prompt = decodeURIComponent(searchParams.get('prompt') || '');
  const [mode, setMode] = useState<'building' | 'executing'>('building');
  const [newPrompt, setNewPrompt] = useState('');

  useEffect(() => {
    if (status === 'loading') return;

    // If no prompt provided, redirect to homepage
    if (!prompt.trim()) {
      router.push('/app');
      return;
    }

    if (!session?.user) {
      // Not logged in, redirect to login with redirect and prompt
      router.push(`/auth/login?redirect=/app/worksheet&prompt=${encodeURIComponent(prompt)}`);
    } else {
      const userType = (session.user as any)?.userType;
      setMode(userType === 'freelancer' ? 'executing' : 'building');
    }
  }, [session, status, router, prompt]);

  const handleNewPromptSubmit = () => {
    if (!newPrompt.trim()) return;

    // Navigate to new worksheet with new prompt
    router.push(`/app/worksheet?prompt=${encodeURIComponent(newPrompt)}`);

    // Reset the input
    setNewPrompt('');
  };

  const renderNavbar = () => {
    if (!session) return <Navbar1 />;
    const userType = (session.user as any)?.userType;
    if (userType === 'freelancer') return <FreelancerTopNavbar />;
    if (userType === 'commissioner') return <CommissionerTopNavbar />;
    return <Navbar1 />;
  };

  const backgroundImagePath = `/images/pages/${mode === 'building' ? 'commisioners' : 'freelancers'}.png`;

  // Show loading state while session is being checked
  if (status === 'loading') {
    return (
      <div className="min-h-screen relative overflow-hidden">
        <img
          src={backgroundImagePath}
          alt="Background"
          className="fixed inset-0 w-full h-full object-cover z-0"
        />
        <div className="relative z-10 flex flex-col h-screen">
          <header className="sticky top-0 z-50 bg-white/90 backdrop-blur-sm shadow-sm">
            {renderNavbar()}
          </header>
          <main className="flex-1 overflow-hidden flex items-center justify-center">
            <div className="bg-white/90 backdrop-blur-sm rounded-2xl shadow-lg p-8 border border-white/30">
              <div className="flex items-center justify-center">
                <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-black"></div>
                <span className="ml-3 text-gray-600 text-sm" style={{ fontFamily: 'Plus Jakarta Sans' }}>
                  Loading worksheet...
                </span>
              </div>
            </div>
          </main>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen relative overflow-hidden">
      {/* Background only visible behind worksheet */}
      <img
        src={backgroundImagePath}
        alt="Background"
        className="fixed inset-0 w-full h-full object-cover z-0"
      />

      {/* Content Layer */}
      <div className="relative z-10 flex flex-col h-screen">
        <header className="sticky top-0 z-50 bg-white/90 backdrop-blur-sm shadow-sm">
          {renderNavbar()}
        </header>

        <main className="flex-1 overflow-hidden flex">
          {/* Left Panel: AI Chat Thread */}
          <motion.div
            className="min-w-[320px] max-w-[35%] h-full relative"
            initial={{ x: -50, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.4, ease: 'easeInOut' }}
          >
            <AIChatThread
              prompt={prompt}
              userType={(session?.user as any)?.userType}
            />
          </motion.div>

          {/* Right Panel: Worksheet Content */}
          <motion.div
            className="flex-1 h-full overflow-y-auto bg-white/90 backdrop-blur-sm"
            initial={{ x: 50, opacity: 0 }}
            animate={{ x: 0, opacity: 1 }}
            transition={{ duration: 0.4, ease: 'easeInOut', delay: 0.1 }}
          >
            <div className="h-full flex flex-col">
              {/* New Prompt Input */}
              <div className="p-4 border-b border-gray-200 bg-white/95 backdrop-blur-sm">
                <div className="flex gap-3 items-center">
                  <input
                    type="text"
                    placeholder="Ask something new..."
                    className="flex-1 px-4 py-3 border border-gray-300 rounded-xl focus:outline-none focus:ring-2 focus:ring-pink-500 text-sm"
                    style={{ fontFamily: 'Plus Jakarta Sans' }}
                    value={newPrompt}
                    onChange={(e) => setNewPrompt(e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' && newPrompt.trim()) {
                        handleNewPromptSubmit();
                      }
                    }}
                  />
                  <button
                    onClick={handleNewPromptSubmit}
                    disabled={!newPrompt.trim()}
                    className={`px-6 py-3 rounded-xl text-sm font-medium transition-colors ${
                      newPrompt.trim()
                        ? 'bg-gray-900 text-white hover:bg-gray-800'
                        : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                    }`}
                    style={{ fontFamily: 'Plus Jakarta Sans' }}
                  >
                    Send
                  </button>
                </div>
              </div>

              {/* Worksheet Content */}
              <div className="flex-1 overflow-y-auto">
                {mode === 'executing' ? (
                  <PromptToGigChat prompt={prompt} />
                ) : (
                  <PromptToProjectChat prompt={prompt} />
                )}
              </div>
            </div>
          </motion.div>
        </main>
      </div>
    </div>
  );
}