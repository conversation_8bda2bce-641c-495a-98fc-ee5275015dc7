

'use client';

import { useEffect, useState, useRef } from 'react';
import { useSession } from 'next-auth/react';

interface PromptToGigChatProps {
  prompt: string;
}

interface ChatMessage {
  role: 'user' | 'ai';
  content: string;
}

interface GigOpportunity {
  gigId: number;
  projectName: string;
  budget: string;
  organization: {
    id: number;
    name: string;
    description: string;
    logo?: string;
  };
  skillsRequired: string[];
  description: string;
  category: string;
  subcategory: string;
  tags: string[];
  matchScore: number;
  matchType: string;
  hourlyRateMin?: number;
  hourlyRateMax?: number;
  estimatedHours?: number;
}

/**
 * PromptToGigChat - Freelancer Mode Chat Component
 *
 * FLOW CHECKLIST STATUS:
 * ✅ 1. Prompt → POST to /api/ai-intake/freelancer (Working)
 * ✅ 2. Agent responds with matching gig metadata (Working)
 * ✅ 3. Gig suggestions displayed in worksheet view (Implemented)
 * ✅ 4. On select: show Apply form preview with agent-suggested answers (Implemented)
 * ✅ 5. User can edit fields before submitting (Implemented)
 * ✅ 6. POST form to /api/gigs/gig-applications (Working)
 * ✅ 7. Agent replies in chat: "Application submitted to commissioner" (Implemented)
 *
 * GAPS IDENTIFIED:
 * - Could add more sophisticated form validation
 * - Skills and tools selection could be enhanced with autocomplete
 * - Application preview could show estimated match score
 */
export default function PromptToGigChat({ prompt }: PromptToGigChatProps) {
  const { data: session } = useSession();
  const [chatHistory, setChatHistory] = useState<ChatMessage[]>([
    { role: 'user', content: prompt },
  ]);
  const [loading, setLoading] = useState(false);
  const [opportunities, setOpportunities] = useState<GigOpportunity[]>([]);
  const [selectedGig, setSelectedGig] = useState<GigOpportunity | null>(null);
  const [showApplicationForm, setShowApplicationForm] = useState(false);
  const [applicationData, setApplicationData] = useState({
    pitch: '',
    sampleLinks: [''],
    skills: [] as string[],
    tools: [] as string[]
  });
  const bottomRef = useRef<HTMLDivElement | null>(null);

  // Helper function to add messages with deduplication
  const addChatMessage = (message: ChatMessage) => {
    setChatHistory((prev) => {
      // Check for duplicates based on role and content
      const isDuplicate = prev.some(msg =>
        msg.role === message.role && msg.content === message.content
      );

      if (isDuplicate) {
        console.log('Duplicate message prevented:', message.content.substring(0, 50) + '...');
        return prev;
      }

      return [...prev, message];
    });
  };

  useEffect(() => {
    const runInitialQuery = async () => {
      setLoading(true);
      try {
        const res = await fetch('/api/ai-intake/freelancer', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({ intent: prompt, step: 'initial' }),
        });
        const data = await res.json();
        const reply = data.result || data.error || 'No response received.';

        // Try to parse the JSON response for structured data
        try {
          const parsedResult = JSON.parse(reply);

          // Validate required fields in parsed result
          if (!parsedResult.step || !parsedResult.message) {
            throw new Error('Invalid agent response format: missing step or message');
          }

          if (parsedResult.step === 'opportunities_results') {
            // Validate opportunities data
            if (!Array.isArray(parsedResult.opportunities)) {
              console.warn('⚠️ Agent returned invalid opportunities data, falling back to text display');
              setChatHistory((prev) => [...prev, { role: 'ai', content: reply }]);
              return;
            }

            // Show gig opportunities
            setOpportunities(parsedResult.opportunities);
            addChatMessage({
              role: 'ai',
              content: parsedResult.message
            });
          } else {
            // Default handling for other responses
            addChatMessage({ role: 'ai', content: reply });
          }
        } catch (parseError) {
          // If not JSON or invalid format, treat as plain text
          console.warn('⚠️ Failed to parse agent response as JSON:', parseError);
          addChatMessage({ role: 'ai', content: reply });
        }
      } catch (err) {
        addChatMessage({ role: 'ai', content: 'Something went wrong. Please try again.' });
      } finally {
        setLoading(false);
      }
    };

    runInitialQuery();
  }, [prompt]);

  useEffect(() => {
    bottomRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [chatHistory, loading]);

  // Persist chat session after initial AI reply
  useEffect(() => {
    if (chatHistory.length < 2) return; // Only send once both user + ai message exist

    const saveSession = async () => {
      const userId = (await import('next-auth/react')).useSession().data?.user?.id;
      if (!userId) return;

      try {
        await fetch('/api/chat-history', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            userId,
            prompt,
            messages: chatHistory
          })
        });
      } catch (err) {
        console.error('Failed to save chat session:', err);
      }
    };

    saveSession();
  }, [chatHistory]);

  const handleGigSelect = async (gig: GigOpportunity) => {
    setSelectedGig(gig);
    setShowApplicationForm(true);
    setLoading(true);

    // Add initial message
    addChatMessage({
      role: 'ai',
      content: `Great choice! I'll help you apply for "${gig.projectName}" at ${gig.organization.name}. Let me generate a draft application for you...`
    });

    try {
      // Call AI intake API to generate pitch
      const res = await fetch('/api/ai-intake/freelancer', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          intent: 'apply',
          selectedGigId: gig.gigId
        }),
      });

      const data = await res.json();

      if (data.result) {
        // Try to parse the AI response for structured data
        try {
          const parsedResult = JSON.parse(data.result);

          if (parsedResult.pitch) {
            // Pre-fill the application form with AI-generated content
            setApplicationData(prev => ({
              ...prev,
              pitch: parsedResult.pitch,
              skills: parsedResult.skills || prev.skills,
              tools: parsedResult.tools || prev.tools,
              sampleLinks: parsedResult.sampleLinks || prev.sampleLinks
            }));
          }
        } catch (parseError) {
          // If parsing fails, use the raw result as pitch
          setApplicationData(prev => ({
            ...prev,
            pitch: data.result
          }));
        }

        // Add AI response to chat with deduplication
        addChatMessage({
          role: 'ai',
          content: "Here's a draft you can send or edit:"
        });
      } else {
        // Fallback message if AI doesn't provide a result
        addChatMessage({
          role: 'ai',
          content: "Please fill out the application form below. I'll help you craft a compelling pitch!"
        });
      }
    } catch (error) {
      console.error('Failed to generate AI pitch:', error);
      addChatMessage({
        role: 'ai',
        content: "I encountered an error generating your pitch, but you can still fill out the form manually. I'm here to help!"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleApplicationSubmit = async () => {
    // Validation checks
    if (!selectedGig) {
      addChatMessage({
        role: 'ai',
        content: '❌ No gig selected. Please select a gig first.'
      });
      return;
    }

    if (!applicationData.pitch.trim()) {
      addChatMessage({
        role: 'ai',
        content: '❌ Please provide a pitch for your application.'
      });
      return;
    }

    if (!session?.user?.id) {
      addChatMessage({
        role: 'ai',
        content: '❌ You must be logged in to submit an application.'
      });
      return;
    }

    setLoading(true);

    try {
      const applicationPayload = {
        gigId: selectedGig.gigId,
        freelancerId: parseInt(session.user.id),
        pitch: applicationData.pitch.trim(),
        sampleLinks: applicationData.sampleLinks.filter(link => link.trim()),
        skills: applicationData.skills,
        tools: applicationData.tools
      };

      const res = await fetch('/api/gigs/gig-applications', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(applicationPayload),
      });

      const result = await res.json();

      if (res.ok) {
        addChatMessage({
          role: 'ai',
          content: `🎉 Application submitted successfully! Your application for "${selectedGig.projectName}" has been sent to ${selectedGig.organization.name}. They'll review it and get back to you soon.`
        });

        // Reset form state
        setShowApplicationForm(false);
        setSelectedGig(null);
        setApplicationData({
          pitch: '',
          sampleLinks: [''],
          skills: [],
          tools: []
        });
      } else {
        throw new Error(result.error || `Application submission failed with status ${res.status}`);
      }
    } catch (err) {
      console.error('❌ Application submission failed:', err);
      addChatMessage({
        role: 'ai',
        content: `❌ Failed to submit application: ${err instanceof Error ? err.message : 'Unknown error'}. Please check your connection and try again.`
      });
    } finally {
      setLoading(false);
    }
  };

  const handleCreateProposal = () => {
    window.location.href = '/freelancer-dashboard/proposals/create?fromPrompt=' + encodeURIComponent(prompt);
  };

  const handleFindMoreGigs = () => {
    window.location.href = '/freelancer-dashboard/gigs?prompt=' + encodeURIComponent(prompt);
  };

  return (
    <div className="h-screen w-full bg-black/10 backdrop-blur-sm flex flex-col overflow-hidden">
      <div className="flex-1 overflow-y-auto p-6 space-y-4">
        {chatHistory.map((msg, idx) => (
          <div
            key={idx}
            className={`max-w-xl rounded-xl px-4 py-3 text-sm whitespace-pre-wrap shadow-md transition-all ${
              msg.role === 'user'
                ? 'ml-auto bg-gray-900 text-white'
                : 'mr-auto bg-white/90 backdrop-blur-sm text-gray-900 border border-gray-300'
            }`}
            style={{ fontFamily: 'Plus Jakarta Sans' }}
          >
            {msg.content}
          </div>
        ))}
        {loading && (
          <div className="mr-auto bg-white/90 backdrop-blur-sm text-gray-900 rounded-xl px-4 py-3 text-sm animate-pulse shadow-md border border-gray-300">
            Generating response...
          </div>
        )}
        <div ref={bottomRef} />
      </div>

      {/* Gig Opportunities */}
      {opportunities.length > 0 && !showApplicationForm && (
        <div className="border-t border-gray-200 p-6 bg-white/90 backdrop-blur-sm shadow-lg">
          <div className="space-y-4">
            <h3 className="text-lg font-bold text-gray-900" style={{ fontFamily: 'Plus Jakarta Sans' }}>
              Available Opportunities
            </h3>

            <div className="grid grid-cols-1 gap-3 max-h-60 overflow-y-auto">
              {opportunities.slice(0, 6).map((gig) => (
                <button
                  key={gig.gigId}
                  onClick={() => handleGigSelect(gig)}
                  className="p-4 border border-gray-200 rounded-xl hover:bg-gray-50 transition-colors text-left"
                  style={{ fontFamily: 'Plus Jakarta Sans' }}
                >
                  <div className="flex justify-between items-start gap-3">
                    <div className="flex-1 min-w-0">
                      <div className="font-medium text-sm text-gray-900 truncate">
                        {gig.projectName}
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        {gig.organization.name} • {gig.budget}
                      </div>
                      <div className="text-xs text-gray-600 mt-1 line-clamp-2">
                        {gig.description}
                      </div>
                      {gig.skillsRequired.length > 0 && (
                        <div className="mt-2 flex flex-wrap gap-1">
                          {gig.skillsRequired.slice(0, 3).map((skill, index) => (
                            <span key={index} className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full">
                              {skill}
                            </span>
                          ))}
                          {gig.skillsRequired.length > 3 && (
                            <span className="px-2 py-1 bg-gray-100 text-gray-600 text-xs rounded-full">
                              +{gig.skillsRequired.length - 3} more
                            </span>
                          )}
                        </div>
                      )}
                    </div>
                    <div className="text-xs text-gray-500">
                      {gig.matchScore}% match
                    </div>
                  </div>
                </button>
              ))}
            </div>

            <div className="flex gap-3">
              <button
                onClick={handleCreateProposal}
                className="flex-1 bg-gray-100 text-gray-900 py-3 rounded-xl text-sm font-medium hover:bg-gray-200 transition-colors shadow-md border border-gray-200"
                style={{ fontFamily: 'Plus Jakarta Sans' }}
              >
                Create Custom Proposal
              </button>
              <button
                onClick={handleFindMoreGigs}
                className="flex-1 bg-gray-900 text-white py-3 rounded-xl text-sm font-medium hover:bg-gray-800 transition-colors shadow-md"
                style={{ fontFamily: 'Plus Jakarta Sans' }}
              >
                Browse All Gigs
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Application Form */}
      {showApplicationForm && selectedGig && (
        <div className="border-t border-gray-200 p-6 bg-white/90 backdrop-blur-sm shadow-lg">
          <div className="space-y-4">
            <h3 className="text-lg font-bold text-gray-900" style={{ fontFamily: 'Plus Jakarta Sans' }}>
              Apply for {selectedGig.projectName}
            </h3>

            <div className="space-y-3">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Your Pitch *
                </label>
                <textarea
                  value={applicationData.pitch}
                  onChange={(e) => setApplicationData(prev => ({ ...prev, pitch: e.target.value }))}
                  placeholder="Explain why you're perfect for this project..."
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500 resize-none text-sm"
                  rows={3}
                  style={{ fontFamily: 'Plus Jakarta Sans' }}
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-2">
                  Portfolio Link (Optional)
                </label>
                <input
                  type="url"
                  value={applicationData.sampleLinks[0]}
                  onChange={(e) => setApplicationData(prev => ({
                    ...prev,
                    sampleLinks: [e.target.value, ...prev.sampleLinks.slice(1)]
                  }))}
                  placeholder="https://your-portfolio.com"
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-gray-500 text-sm"
                  style={{ fontFamily: 'Plus Jakarta Sans' }}
                />
              </div>
            </div>

            <div className="flex gap-3">
              <button
                onClick={() => {
                  setShowApplicationForm(false);
                  setSelectedGig(null);
                }}
                className="flex-1 bg-gray-100 text-gray-900 py-3 rounded-xl text-sm font-medium hover:bg-gray-200 transition-colors shadow-md border border-gray-200"
                style={{ fontFamily: 'Plus Jakarta Sans' }}
              >
                Cancel
              </button>
              <button
                onClick={handleApplicationSubmit}
                disabled={!applicationData.pitch.trim() || loading}
                className="flex-1 bg-gray-900 text-white py-3 rounded-xl text-sm font-medium hover:bg-gray-800 transition-colors shadow-md disabled:opacity-50 disabled:cursor-not-allowed"
                style={{ fontFamily: 'Plus Jakarta Sans' }}
              >
                {loading ? 'Submitting...' : 'Submit Application'}
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Default Actions */}
      {opportunities.length === 0 && !showApplicationForm && (
        <div className="border-t border-gray-200 p-6 bg-white/90 backdrop-blur-sm shadow-lg">
          <div className="flex justify-end gap-3">
            <button
              onClick={handleCreateProposal}
              className="bg-gray-900 text-white px-6 py-3 rounded-xl text-sm font-medium hover:bg-gray-800 transition-colors shadow-md"
              style={{ fontFamily: 'Plus Jakarta Sans' }}
            >
              Create Proposal
            </button>
            <button
              onClick={handleFindMoreGigs}
              className="bg-gray-50 text-gray-900 px-6 py-3 rounded-xl text-sm font-medium hover:bg-gray-100 transition-colors shadow-md border border-gray-200"
              style={{ fontFamily: 'Plus Jakarta Sans' }}
            >
              Find Matching Gigs
            </button>
          </div>
        </div>
      )}
    </div>
  );
}