'use client';

import { useEffect, useRef, useState } from 'react';

interface ChatMessage {
  role: 'user' | 'ai';
  content: string;
}

export interface AiChatThreadProps {
  prompt: string;
  userType: 'freelancer' | 'commissioner';
}

export default function AiChatThread({ prompt, userType }: AiChatThreadProps) {
  const [messages, setMessages] = useState<ChatMessage[]>([
    { role: 'user', content: prompt }
  ]);
  const [loading, setLoading] = useState(false);
  const bottomRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    bottomRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Initialize chat with AI response to the prompt
  useEffect(() => {
    if (!prompt.trim()) return;

    const initializeChat = async () => {
      setLoading(true);
      try {
        const apiEndpoint = userType === 'freelancer'
          ? '/api/ai-intake/freelancer'
          : '/api/ai-intake/client';

        const res = await fetch(apiEndpoint, {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            intent: prompt,
            step: 'initial',
            prompt: userType === 'commissioner' ? prompt : undefined
          }),
        });

        const data = await res.json();
        const reply = data.result || data.error || 'No response received.';

        // Add AI response to chat, avoiding duplicates
        setMessages(prev => {
          const aiMessage = { role: 'ai' as const, content: reply };
          const isDuplicate = prev.some(msg => msg.role === 'ai' && msg.content === reply);
          if (isDuplicate) return prev;
          return [...prev, aiMessage];
        });
      } catch (error) {
        console.error('Failed to initialize chat:', error);
        setMessages(prev => [...prev, {
          role: 'ai',
          content: 'Sorry, I encountered an error. Please try again.'
        }]);
      } finally {
        setLoading(false);
      }
    };

    initializeChat();
  }, [prompt, userType]);

  return (
    <aside className="min-w-[320px] max-w-[35%] h-screen overflow-y-auto bg-black text-white flex flex-col border-r border-white/10 p-4 sticky top-0">
      <h2 className="text-lg font-semibold mb-4" style={{ fontFamily: 'Plus Jakarta Sans' }}>AI Thread</h2>
      <div className="space-y-4 flex-1 overflow-y-auto">
        {messages.map((msg, idx) => (
          <div
            key={idx}
            className={`rounded-xl px-4 py-2 text-sm whitespace-pre-wrap max-w-[90%] ${
              msg.role === 'user'
                ? 'ml-auto bg-white text-black'
                : 'mr-auto bg-gray-800 text-white'
            }`}
            style={{ fontFamily: 'Plus Jakarta Sans' }}
          >
            {msg.content}
          </div>
        ))}
        {loading && (
          <div className="mr-auto bg-gray-800 text-white rounded-xl px-4 py-2 text-sm max-w-[90%]">
            <div className="flex items-center gap-2">
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
              <span>AI is thinking...</span>
            </div>
          </div>
        )}
        <div ref={bottomRef} />
      </div>
    </aside>
  );
}
